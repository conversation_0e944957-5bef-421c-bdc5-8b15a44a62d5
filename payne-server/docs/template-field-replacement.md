# 模板字段替换功能说明

## 概述

在后端 `BatchPrintServiceImpl` 中实现了模板字段替换功能，当调用 `/api/batch-print/generate` 接口时，后端会自动将模板中的字段变量（如 `{bankName}`、`{gradeScore}` 等）替换为对应钱币的实际数据值。

## 功能特点

1. **自动字段替换**：支持将 `{fieldName}` 格式的变量替换为实际数据
2. **复杂字段组合**：支持 `{serialNumber}-{version}` 等复杂字段组合
3. **批量处理**：为每个钱币生成对应的处理后模板
4. **错误处理**：包含完善的异常处理机制

## 支持的字段变量

### 基础信息字段
| 变量 | 说明 | 数据来源 | 示例值 |
|------|------|----------|--------|
| `{bankName}` | 发行银行 | `coin.getBankName()` | "中国人民银行" |
| `{coinName}` | 钱币名称（组合） | `getCombinedCoinName()` | "2023年熊猫金币 30克 金币" |
| `{coinName1}` | 钱币名称1 | `coin.getCoinName1()` | "2023年熊猫金币" |
| `{coinName2}` | 钱币名称2 | `coin.getCoinName2()` | "30克" |
| `{coinName3}` | 钱币名称3 | `coin.getCoinName3()` | "金币" |
| `{yearInfo}` | 年代信息 | `coin.getYearInfo()` | "2023年" |
| `{serialNumber}` | 序列号 | `coin.getSerialNumber()` | "PD2023001" |
| `{version}` | 版别 | `coin.getVersion()` | "普制版" |

### 评级信息字段
| 变量 | 说明 | 数据来源 | 示例值 |
|------|------|----------|--------|
| `{gradeScore}` | 评级分数 | `coin.getGradeScore()` | "MS68" |
| `{gradeLevel}` | 评级等级 | `coin.getRank()` | "Superb Gem Unc" |
| `{specialMark}` | 特殊标记 | `coin.getSpecialMark()` | "EPQ" |
| `{authenticity}` | 真伪性 | `coin.getAuthenticity()` | "真品" |

### 其他字段
| 变量 | 说明 | 数据来源 | 示例值 |
|------|------|----------|--------|
| `{diyCode}` | 送评条码 | `coin.getDiyCode()` | "DIY2023001" |
| `{qrcode}` | 二维码内容 | `coin.getDiyCode()` | "DIY2023001" |
| `{qrCodeContent}` | 二维码内容 | `coin.getDiyCode()` | "DIY2023001" |
| `{customerName}` | 客户姓名 | `getCustomerName()` | "张三" |
| `{weight}` | 重量 | `coin.getCoinWeight()` | "30g" |
| `{size}` | 尺寸 | `coin.getCoinSize()` | "32mm" |
| `{coinType}` | 钱币类型 | `coin.getCoinType()` | "金币" |

### 复杂字段组合
| 变量 | 说明 | 处理逻辑 | 示例值 |
|------|------|----------|--------|
| `{serialNumber}-{version}` | 编号-版别组合 | 智能组合，处理空值 | "PD2023001-普制版" |

## 使用方法

### 1. 模板配置

在模板的 `layoutConfig` 中使用字段变量：

```json
{
  "panels": [
    {
      "printElements": [
        {
          "options": {
            "field": "{bankName}",
            "title": "银行名称"
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        },
        {
          "options": {
            "field": "{gradeScore}",
            "title": "68"
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        },
        {
          "options": {
            "field": "{qrcode}",
            "title": "二维码"
          },
          "printElementType": {
            "title": "二维码",
            "type": "qrcode"
          }
        },
        {
          "options": {
            "field": "{serialNumber}-{version}",
            "title": "钱币编号-版别"
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        }
      ]
    }
  ]
}
```

### 2. API 调用

调用批量打印接口：

```http
POST /api/batch-print/generate
Content-Type: application/json

{
  "coinIds": ["coin001", "coin002"],
  "templateType": "CUSTOM",
  "customTemplateId": "template123",
  "conversionType": 0,
  "printType": "preview"
}
```

### 3. 返回数据

接口返回的 `PrintDataVO` 包含：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalCount": 2,
    "templateId": "template123",
    "templateName": "标准标签模板",
    "layoutConfig": { /* 原始模板配置 */ },
    "processedTemplates": [
      { /* 第一个钱币对应的处理后模板 */ },
      { /* 第二个钱币对应的处理后模板 */ }
    ],
    "items": [
      { /* 第一个钱币数据 */ },
      { /* 第二个钱币数据 */ }
    ]
  }
}
```

## 核心实现

### 1. 字段替换逻辑

```java
private String replaceFieldVariables(String text, PjOSendformItem coin) {
    if (text == null || !text.contains("{")) {
        return text;
    }
    
    String result = text;
    Pattern pattern = Pattern.compile("\\{(\\w+)\\}");
    Matcher matcher = pattern.matcher(text);
    
    while (matcher.find()) {
        String fullMatch = matcher.group(0); // {bankName}
        String fieldName = matcher.group(1); // bankName
        
        Object fieldValue = getFieldValue(coin, fieldName);
        String replacementValue = fieldValue != null ? fieldValue.toString() : "";
        
        result = result.replace(fullMatch, replacementValue);
    }
    
    return result;
}
```

### 2. 复杂字段处理

```java
private String processComplexFieldCombination(String text, PjOSendformItem coin) {
    // 特殊处理 {serialNumber}-{version} 组合
    if (text.contains("{serialNumber}") && text.contains("{version}")) {
        String serialNumber = coin.getSerialNumber() != null ? coin.getSerialNumber() : "";
        String version = coin.getVersion() != null ? coin.getVersion() : "";
        
        if (!serialNumber.isEmpty() && !version.isEmpty()) {
            return text.replace("{serialNumber}-{version}", serialNumber + "-" + version);
        }
        // 处理空值情况...
    }
    
    return replaceFieldVariables(text, coin);
}
```

## 测试接口

提供了测试接口用于验证功能：

1. **字段替换测试**：`GET /api/template-test/field-replacement`
2. **支持字段列表**：`GET /api/template-test/supported-fields`
3. **示例模板**：`GET /api/template-test/example-template`

## 注意事项

1. **字段名称区分大小写**：`{bankName}` 和 `{BankName}` 是不同的
2. **空值处理**：如果字段值为空，会替换为空字符串
3. **复杂字段**：支持多个变量组合，如 `{serialNumber}-{version}`
4. **错误处理**：如果模板处理失败，会使用原始模板
5. **性能考虑**：使用深拷贝确保不修改原始模板数据

## 扩展字段

如需添加新的字段变量，请修改 `getFieldValue` 方法：

```java
private Object getFieldValue(PjOSendformItem coin, String fieldName) {
    switch (fieldName) {
        // 现有字段...
        case "newField": return coin.getNewField();
        default: return "";
    }
}
```
