package com.payne.server.banknote.controller;

import com.payne.server.banknote.service.impl.BatchPrintServiceImpl;
import com.payne.server.common.core.web.BaseController;
import com.payne.server.common.core.web.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 模板字段替换测试控制器
 * 用于测试和验证模板字段替换功能
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
@Slf4j
@RestController
@RequestMapping("/api/template-test")
@RequiredArgsConstructor
public class TemplateTestController extends BaseController {

    private final BatchPrintServiceImpl batchPrintService;

    /**
     * 测试字段替换功能
     */
    @GetMapping("/field-replacement")
    public ApiResult<?> testFieldReplacement() {
        try {
            log.info("开始测试字段替换功能");
            batchPrintService.testFieldReplacement();
            return success("字段替换测试完成，请查看日志");
        } catch (Exception e) {
            log.error("字段替换测试失败", e);
            return fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试模板处理
     */
    @PostMapping("/template-processing")
    public ApiResult<?> testTemplateProcessing(@RequestBody Map<String, Object> request) {
        try {
            // 获取请求参数
            @SuppressWarnings("unchecked")
            Map<String, Object> templateData = (Map<String, Object>) request.get("template");
            @SuppressWarnings("unchecked")
            Map<String, Object> coinData = (Map<String, Object>) request.get("coinData");

            if (templateData == null || coinData == null) {
                return fail("请提供模板数据和钱币数据");
            }

            // 创建测试结果
            Map<String, Object> result = new HashMap<>();
            result.put("originalTemplate", templateData);
            result.put("coinData", coinData);
            result.put("message", "模板处理功能已实现，请通过 /api/batch-print/generate 接口测试完整功能");

            return success(result);
        } catch (Exception e) {
            log.error("模板处理测试失败", e);
            return fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取支持的字段列表
     */
    @GetMapping("/supported-fields")
    public ApiResult<?> getSupportedFields() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 基础字段
            List<Map<String, String>> basicFields = Arrays.asList(
                createFieldInfo("bankName", "发行银行", "中国人民银行"),
                createFieldInfo("coinName", "钱币名称", "2023年熊猫金币"),
                createFieldInfo("coinName1", "钱币名称1", "2023年熊猫金币"),
                createFieldInfo("coinName2", "钱币名称2", "30克"),
                createFieldInfo("coinName3", "钱币名称3", "金币"),
                createFieldInfo("yearInfo", "年代信息", "2023年"),
                createFieldInfo("serialNumber", "序列号", "PD2023001"),
                createFieldInfo("version", "版别", "普制版")
            );

            // 评级字段
            List<Map<String, String>> gradeFields = Arrays.asList(
                createFieldInfo("gradeScore", "评级分数", "MS68"),
                createFieldInfo("gradeLevel", "评级等级", "Superb Gem Unc"),
                createFieldInfo("specialMark", "特殊标记", "EPQ"),
                createFieldInfo("authenticity", "真伪性", "真品")
            );

            // 其他字段
            List<Map<String, String>> otherFields = Arrays.asList(
                createFieldInfo("diyCode", "送评条码", "DIY2023001"),
                createFieldInfo("qrcode", "二维码内容", "DIY2023001"),
                createFieldInfo("qrCodeContent", "二维码内容", "DIY2023001"),
                createFieldInfo("customerName", "客户姓名", "张三"),
                createFieldInfo("weight", "重量", "30g"),
                createFieldInfo("size", "尺寸", "32mm"),
                createFieldInfo("coinType", "钱币类型", "金币")
            );

            // 复杂字段组合
            List<Map<String, String>> complexFields = Arrays.asList(
                createFieldInfo("serialNumber}-{version", "编号-版别组合", "PD2023001-普制版")
            );

            result.put("basicFields", basicFields);
            result.put("gradeFields", gradeFields);
            result.put("otherFields", otherFields);
            result.put("complexFields", complexFields);
            result.put("usage", "在模板中使用 {fieldName} 格式，如 {bankName}、{gradeScore} 等");

            return success(result);
        } catch (Exception e) {
            log.error("获取支持字段列表失败", e);
            return fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 创建字段信息
     */
    private Map<String, String> createFieldInfo(String fieldName, String description, String example) {
        Map<String, String> fieldInfo = new HashMap<>();
        fieldInfo.put("fieldName", fieldName);
        fieldInfo.put("variable", "{" + fieldName + "}");
        fieldInfo.put("description", description);
        fieldInfo.put("example", example);
        return fieldInfo;
    }

    /**
     * 生成示例模板
     */
    @GetMapping("/example-template")
    public ApiResult<?> getExampleTemplate() {
        try {
            Map<String, Object> exampleTemplate = new HashMap<>();
            
            // 创建面板
            List<Map<String, Object>> panels = new ArrayList<>();
            Map<String, Object> panel = new HashMap<>();
            panel.put("index", 0);
            panel.put("name", 1);
            panel.put("height", 26);
            panel.put("width", 191);

            // 创建打印元素
            List<Map<String, Object>> printElements = new ArrayList<>();

            // 银行名称元素
            printElements.add(createPrintElement(
                114, 6, 141, 9.75, "银行名称", "{bankName}", "text"
            ));

            // 评级分数元素
            printElements.add(createPrintElement(
                369, 1.5, 75, 42, "68", "{gradeScore}", "text"
            ));

            // 二维码元素
            printElements.add(createPrintElement(
                465, 1.5, 66, 58.5, "二维码", "{qrcode}", "qrcode"
            ));

            // 特殊标记元素
            printElements.add(createPrintElement(
                450, 12, 9, 40.5, "EPQ", "{specialMark}", "text"
            ));

            // 钱币名称元素
            printElements.add(createPrintElement(
                115.5, 27, 139.5, 9.75, "钱币名称1", "{coinName1}", "text"
            ));

            // 复杂字段元素
            printElements.add(createPrintElement(
                115.5, 49.5, 139.5, 9.75, "钱币编号-版别", "{serialNumber}-{version}", "text"
            ));

            panel.put("printElements", printElements);
            panels.add(panel);
            exampleTemplate.put("panels", panels);

            Map<String, Object> result = new HashMap<>();
            result.put("template", exampleTemplate);
            result.put("description", "这是一个示例模板，展示了如何使用字段变量");
            result.put("usage", "将此模板保存到数据库，然后通过 /api/batch-print/generate 接口测试");

            return success(result);
        } catch (Exception e) {
            log.error("生成示例模板失败", e);
            return fail("生成失败：" + e.getMessage());
        }
    }

    /**
     * 创建打印元素
     */
    private Map<String, Object> createPrintElement(double left, double top, double width, double height,
                                                  String title, String field, String type) {
        Map<String, Object> element = new HashMap<>();
        Map<String, Object> options = new HashMap<>();
        Map<String, Object> printElementType = new HashMap<>();

        options.put("left", left);
        options.put("top", top);
        options.put("width", width);
        options.put("height", height);
        options.put("title", title);
        options.put("field", field);

        printElementType.put("title", getTypeTitle(type));
        printElementType.put("type", type);

        element.put("options", options);
        element.put("printElementType", printElementType);

        return element;
    }

    /**
     * 获取类型标题
     */
    private String getTypeTitle(String type) {
        switch (type) {
            case "text": return "文本";
            case "qrcode": return "二维码";
            case "rect": return "矩形";
            default: return "未知";
        }
    }
}
