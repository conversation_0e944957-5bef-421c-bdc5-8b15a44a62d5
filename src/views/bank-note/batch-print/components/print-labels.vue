<template>
  <!-- 右侧滑出的打印标签抽屉 -->
  <el-drawer
    v-model="visible"
    title="打印标签预览"
    direction="rtl"
    size="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">打印标签预览</span>
        <div class="drawer-actions">
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div class="print-page">
        <!-- 自定义模板渲染 -->
        <div class="custom-template-container">
          <div
            v-for="(item, index) in printData?.items || []"
            :key="item.id || index"
            class="custom-label-item"
            :style="getCustomLabelStyle()"
          >
            <div
              v-for="zone in getTemplateZones()"
              :key="zone.id"
              class="template-zone"
              :style="getZoneStyle(zone)"
            >
              <!-- 特殊处理评级信息区域 -->
              <div v-if="zone.id === 'gradeInfo' && zone.multiFieldDisplay" class="grade-info-container">
                <div
                  v-for="(displayConfig, index) in zone.multiFieldDisplay"
                  :key="`${displayConfig.field}-${index}`"
                  class="grade-display-item"
                  :class="`grade-${displayConfig.position}`"
                  :style="getGradeDisplayStyle(displayConfig)"
                  v-html="formatGradeDisplay(item[displayConfig.field], displayConfig)"
                >
                </div>
              </div>

              <!-- 普通字段显示 -->
              <div v-else>
                <div
                  v-for="field in getZoneFields(zone.id, item)"
                  :key="field.name"
                  class="zone-field"
                  :style="getFieldStyle(zone, field)"
                >
                  <!-- 二维码字段特殊处理 -->
                  <div v-if="isQRCodeField(field.name)" v-html="formatQRCodeDisplay(field, zone)"></div>
                  <!-- 普通字段 -->
                  <div v-else v-html="formatFieldDisplay(field, zone)"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </el-drawer>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { Printer, Download } from '@element-plus/icons-vue';
  // import QRCodeComponent from '@/components/QRCodeComponent.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    printData: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['update:modelValue', 'confirm-print']);

  // 响应式数据
  const loading = ref(false);
  const printing = ref(false);
  const downloading = ref(false);

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  // 处理打印
  const handlePrint = async () => {
    printing.value = true;
    try {
      // 使用浏览器打印
      window.print();
      emit('confirm-print', {
        ...props.printData,
        action: 'print'
      });
    } catch (error) {
      EleMessage.error('打印失败：' + error.message);
    } finally {
      printing.value = false;
    }
  };

  // 处理下载
  const handleDownload = async () => {
    downloading.value = true;
    try {
      emit('confirm-print', {
        ...props.printData,
        action: 'download'
      });
    } catch (error) {
      EleMessage.error('下载失败：' + error.message);
    } finally {
      downloading.value = false;
    }
  };

  // 获取模板区域配置
  const getTemplateZones = () => {
    if (!props.printData?.layoutConfig?.zones) {
      console.warn('模板区域配置缺失，使用默认配置');
      return getDefaultZones();
    }
    return props.printData.layoutConfig.zones;
  };

  // 默认区域配置（当模板配置缺失时使用）
  const getDefaultZones = () => {
    return [
      {
        id: 'logo',
        name: '公司Logo',
        x: 0,
        y: 0,
        width: 30,
        height: 25,
        fontSize: 12,
        color: '#333333',
        backgroundColor: '#f5f5f5'
      },
      {
        id: 'coinInfo',
        name: '钱币信息',
        x: 30,
        y: 0,
        width: 80,
        height: 25,
        fontSize: 12,
        color: '#333333',
        backgroundColor: '#ffffff'
      },
      {
        id: 'gradeInfo',
        name: '评级信息',
        x: 110,
        y: 0,
        width: 40,
        height: 25,
        fontSize: 12,
        color: '#333333',
        backgroundColor: '#ffffff'
      }
    ];
  };

  // 获取自定义标签容器样式
  const getCustomLabelStyle = () => {
    const canvas = props.printData?.layoutConfig?.canvas;
    return {
      position: 'relative',
      width: canvas?.width ? `${canvas.width}mm` : '200mm',
      height: canvas?.height ? `${canvas.height}mm` : '25mm',
      border: '1px solid #dcdfe6',
      marginBottom: '8px',
      backgroundColor: '#ffffff',
      pageBreakInside: 'avoid'
    };
  };

  // 获取区域样式
  const getZoneStyle = (zone) => {
    return {
      position: 'absolute',
      left: `${zone.x || 0}mm`,
      top: `${zone.y || 0}mm`,
      width: `${zone.width || 20}mm`,
      height: `${zone.height || 10}mm`,
      fontSize: `${zone.fontSize || 12}px`,
      color: zone.color || '#333333',
      backgroundColor: zone.backgroundColor || 'transparent',
      fontWeight: zone.fontWeight || 'normal',
      textAlign: zone.textAlign || 'left',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: zone.verticalAlign || 'flex-start',
      padding: zone.padding ? `${zone.padding}px` : '2px',
      border: zone.showBorder ? `1px solid ${zone.borderColor || '#ccc'}` : 'none',
      borderRadius: zone.borderRadius ? `${zone.borderRadius}px` : '0',
      overflow: 'hidden',
      lineHeight: zone.lineHeight || 1.2
    };
  };

  // 获取字段样式
  const getFieldStyle = (zone, field) => {
    // 获取字段样式配置
    const fieldStyle = zone?.fieldStyles?.[field.name] || {};

    // 为分离的评级字段设置默认字体大小
    let defaultFontSize = 'inherit';
    if (field.name === 'gradeScoreNumber') {
      defaultFontSize = '28px'; // 数字部分默认较大
    } else if (field.name === 'gradeScoreText') {
      defaultFontSize = '12px'; // 文字部分默认较小
    }

    return {
      lineHeight: zone.lineHeight || 1.2,
      marginBottom: zone.fieldSpacing ? `${zone.fieldSpacing}px` : '1px',
      wordBreak: 'break-all',
      fontSize: fieldStyle.fontSize ? `${fieldStyle.fontSize}px` : (field.fontSize ? `${field.fontSize}px` : defaultFontSize),
      fontWeight: fieldStyle.fontWeight || field.fontWeight || 'inherit',
      color: fieldStyle.color || field.color || 'inherit'
    };
  };

  // 获取区域字段数据
  const getZoneFields = (zoneId, item) => {
    try {
      // 获取字段映射配置
      const fieldMapping = props.printData?.fieldMapping?.[zoneId] || [];

      if (!fieldMapping.length) {
        console.warn(`区域 ${zoneId} 没有配置字段映射`);
        return [];
      }

      // 获取区域配置信息
      const zone = props.printData?.layoutConfig?.zones?.find(z => z.id === zoneId);

      // 从钱币数据中提取字段值
      return fieldMapping.map(fieldName => {
        let fieldValue = '';

        // 优先从自定义字段中获取
        if (item.customFields?.[zoneId]?.[fieldName]) {
          fieldValue = item.customFields[zoneId][fieldName];
        } else {
          // 从钱币基础数据中获取
          fieldValue = getFieldValueFromItem(item, fieldName, zone);
        }

        return {
          name: fieldName,
          value: fieldValue || '',
          displayName: getFieldDisplayName(fieldName, zone)
        };
      });
    } catch (error) {
      console.error(`获取区域 ${zoneId} 字段数据失败:`, error);
      return [];
    }
  };

  // 从钱币数据中获取字段值
  const getFieldValueFromItem = (item, fieldName, zone) => {
    // 检查是否是组合字段
    if (zone && zone.combinationFields && zone.combinationFields[fieldName]) {
      return formatCombinationField(item, zone.combinationFields[fieldName]);
    }

    const fieldMap = {
      // 基础信息
      'bankName': item.bankName,
      'coinName': item.coinName,
      'coinName1': item.coinName,
      'yearInfo': item.yearInfo,
      'serialNumber': item.serialNumber,
      'version': item.version,
      'additionalInfo': item.additionalInfo,

      // 特殊组合字段：编号-版别（保持向下兼容）
      'serialNumberWithVersion': formatSerialNumberWithVersion(item),

      // 评级信息
      'gradeScore': item.gradeScore,
      'gradeScoreNumber': extractGradeScoreNumber(item.gradeScore), // 仅数字部分
      'gradeScoreText': extractGradeScoreText(item.gradeScore, item), // 仅文字部分，传入完整item以便获取其他字段
      'gradeLevel': item.gradeLevel,
      'specialMark': item.specialMark,
      'authenticity': item.authenticity,

      // 客户信息
      'customerName': item.customerName,
      'diyCode': item.diyCode,

      // 物理属性
      'weight': item.weight,
      'size': item.size,
      'coinType': item.coinType,

      // 其他
      'diyCode': item.diyCode,
      'printStatus': item.printStatus
    };

    return fieldMap[fieldName] || '';
  };

  // 格式化组合字段
  const formatCombinationField = (item, combinationConfig) => {
    // 直接获取基础字段值，避免递归调用
    const firstValue = getBasicFieldValue(item, combinationConfig.firstField) || '';
    const secondValue = getBasicFieldValue(item, combinationConfig.secondField) || '';

    if (combinationConfig.layout === 'vertical') {
      // 分行显示
      const parts = [];
      if (firstValue) parts.push(firstValue);
      if (secondValue) parts.push(secondValue);
      return parts.join('\n');
    } else {
      // 同一行显示
      if (firstValue && secondValue) {
        return `${firstValue}${combinationConfig.separator}${secondValue}`;
      } else if (firstValue) {
        return firstValue;
      } else if (secondValue) {
        return secondValue;
      }
      return '';
    }
  };

  // 获取基础字段值（不处理组合字段）
  const getBasicFieldValue = (item, fieldName) => {
    const fieldMap = {
      // 基础信息
      'bankName': item.bankName,
      'coinName': item.coinName,
      'coinName1': item.coinName,
      'yearInfo': item.yearInfo,
      'serialNumber': item.serialNumber,
      'version': item.version,
      'additionalInfo': item.additionalInfo,

      // 评级信息
      'gradeScore': item.gradeScore,
      'gradeScoreNumber': extractGradeScoreNumber(item.gradeScore), // 仅数字部分
      'gradeScoreText': extractGradeScoreText(item.gradeScore, item), // 仅文字部分，传入完整item以便获取其他字段
      'gradeLevel': item.gradeLevel,
      'specialMark': item.specialMark,
      'authenticity': item.authenticity,

      // 客户信息
      'customerName': item.customerName,
      'diyCode': item.diyCode,

      // 物理属性
      'weight': item.weight,
      'size': item.size,
      'coinType': item.coinType,

      // 其他
      'diyCode': item.diyCode,
      'printStatus': item.printStatus,

      // 二维码相关
      'qrCode': item.diyCode, // 二维码内容使用送评条码
      'qrCodeContent': item.diyCode
    };

    return fieldMap[fieldName] || '';
  };

  // 格式化编号-版别（保持向下兼容）
  const formatSerialNumberWithVersion = (item) => {
    const serialNumber = item.serialNumber || '';
    const version = item.version || '';

    if (serialNumber && version) {
      return `${serialNumber} - ${version}`;
    } else if (serialNumber) {
      return serialNumber;
    } else if (version) {
      return version;
    }
    return '';
  };

  // 获取字段显示名称
  const getFieldDisplayName = (fieldName, zone) => {
    // 检查是否是组合字段
    if (zone && zone.combinationFields && zone.combinationFields[fieldName]) {
      return zone.combinationFields[fieldName].displayName || fieldName;
    }

    const displayNames = {
      'bankName': '发行银行',
      'coinName': '钱币名称',
      'coinName1': '钱币名称',
      'yearInfo': '年代信息',
      'serialNumber': '序列号',
      'version': '版别',
      'serialNumberWithVersion': '编号-版别',
      'additionalInfo': '附加信息',
      'gradeScore': '评级分数',
      'gradeScoreNumber': '评级分数（仅数字）',
      'gradeScoreText': '评级等级（仅文字）',
      'gradeLevel': '评级等级',
      'specialMark': '特殊标记',
      'authenticity': '真伪性',
      'customerName': '客户姓名',
      'diyCode': '送评条码',
      'weight': '重量',
      'size': '尺寸',
      'coinType': '钱币类型',
      'diyCode': '送评条码',
      'printStatus': '打印状态',
      'qrCode': '二维码',
      'qrCodeContent': '二维码内容'
    };

    return displayNames[fieldName] || fieldName;
  };

  // 解析评级信息
  const parseGradeInfo = (gradeScore) => {
    if (!gradeScore) return { score: '', level: '', mark: '' };

    const value = gradeScore.toString().trim();
    console.log('解析评级信息:', value);

    // 提取数字分数（支持整数和小数，可能在末尾或开头）
    let score = '';
    let levelPart = '';

    // 情况1: 数字在末尾 (如 "Superb Gem Unc68")
    const endScoreMatch = value.match(/^(.+?)(\d+(?:\.\d+)?)$/);
    if (endScoreMatch) {
      levelPart = endScoreMatch[1].trim();
      score = endScoreMatch[2];
      console.log('数字在末尾:', { score, levelPart });
    } else {
      // 情况2: 数字在开头 (如 "68 Superb Gem Unc")
      const startScoreMatch = value.match(/^(\d+(?:\.\d+)?)\s*(.+)$/);
      if (startScoreMatch) {
        score = startScoreMatch[1];
        levelPart = startScoreMatch[2].trim();
        console.log('数字在开头:', { score, levelPart });
      } else {
        // 情况3: 只有数字 (如 "68")
        const onlyNumberMatch = value.match(/^\d+(?:\.\d+)?$/);
        if (onlyNumberMatch) {
          score = value;
          levelPart = '';
          console.log('只有数字:', { score, levelPart });
        } else {
          // 情况4: 只有文字 (如 "Superb Gem Unc")
          score = '';
          levelPart = value;
          console.log('只有文字:', { score, levelPart });
        }
      }
    }

    // 解析等级部分，保持完整的等级描述
    const level = levelPart || '';

    console.log('最终解析结果:', { score, level });
    return { score, level, mark: '' };
  };

  // 格式化字段显示
  const formatFieldDisplay = (field, zone) => {
    const fieldStyle = zone?.fieldStyles?.[field.name];
    const value = field.value || '';

    // 特殊处理二维码字段
    if (field.name === 'qrCode' || field.name === 'qrCodeContent') {
      return formatQRCode(value, fieldStyle);
    }

    if (!fieldStyle || fieldStyle.displayMode === 'normal') {
      return value;
    }

    switch (fieldStyle.displayMode) {
      case 'large':
        return `<span style="font-size: ${fieldStyle.fontSize || 18}px; font-weight: bold;">${value}</span>`;

      case 'vertical':
        // 竖向显示字符，每个字符一行
        const chars = value.split('');
        const letterSpacing = fieldStyle.letterSpacing || 2;
        return chars.map(char =>
          `<div style="line-height: 1; margin-bottom: ${letterSpacing}px;">${char}</div>`
        ).join('');

      case 'grade-layered':
        // 评级信息分层显示：分数大字体在上，等级小字体在下
        const gradeInfo = parseGradeInfo(value);
        const baseFontSize = fieldStyle.fontSize || 12;
        const lineHeight = fieldStyle.lineHeight || 1.2;

        // 配置字体大小：数字部分更大，文字部分更小
        const scoreFontSize = fieldStyle.scoreFontSize || Math.max(24, baseFontSize * 2); // 数字字体，默认24px或基础字体的2倍
        const levelFontSize = fieldStyle.levelFontSize || Math.max(10, baseFontSize * 0.8); // 文字字体，默认10px或基础字体的0.8倍

        console.log('评级分层显示调试:', {
          fieldStyle,
          gradeInfo,
          scoreFontSize,
          levelFontSize,
          configuredScoreFontSize: fieldStyle.scoreFontSize,
          configuredLevelFontSize: fieldStyle.levelFontSize
        });

        if (gradeInfo.score && gradeInfo.level) {
          // 有数字和文字，分层显示
          return `
            <div style="text-align: center; line-height: ${lineHeight};">
              <div style="font-size: ${scoreFontSize}px; font-weight: bold; margin-bottom: 2px;">${gradeInfo.score}</div>
              <div style="font-size: ${levelFontSize}px;">${gradeInfo.level}</div>
            </div>
          `;
        } else if (gradeInfo.score) {
          // 只有数字
          return `<div style="text-align: center; font-size: ${scoreFontSize}px; font-weight: bold;">${gradeInfo.score}</div>`;
        } else if (gradeInfo.level) {
          // 只有文字
          return `<div style="text-align: center; font-size: ${levelFontSize}px;">${gradeInfo.level}</div>`;
        } else {
          // 显示完整内容
          return `<div style="text-align: center; font-size: ${baseFontSize}px;">${value}</div>`;
        }

      case 'grade-score-only':
        // 只显示评级分数
        const scoreInfo = parseGradeInfo(value);
        // const scoreFontSize = fieldStyle.fontSize || 24;
        return scoreInfo.score ?
          `<span style="font-size: ${scoreFontSize}px; font-weight: bold;">${scoreInfo.score}</span>` :
          `<span style="font-size: ${scoreFontSize}px; font-weight: bold;">${value}</span>`;

      case 'grade-level-only':
        // 只显示评级等级
        const levelInfo = parseGradeInfo(value);
        // const levelFontSize = fieldStyle.fontSize || 10;
        return levelInfo.level ?
          `<span style="font-size: ${levelFontSize}px;">${levelInfo.level}</span>` :
          `<span style="font-size: ${levelFontSize}px;">${value}</span>`;

      case 'grade-mark-only':
        // 只显示特殊标记（竖向）
        const markInfo = parseGradeInfo(value);
        const markFontSize = fieldStyle.fontSize || 10;
        const markSpacing = fieldStyle.letterSpacing || 2;

        if (markInfo.mark) {
          const chars = markInfo.mark.split('');
          return chars.map(char =>
            `<div style="line-height: 1; margin-bottom: ${markSpacing}px; font-size: ${markFontSize}px;">${char}</div>`
          ).join('');
        } else {
          return `<span style="font-size: ${markFontSize}px;">${value}</span>`;
        }

      case 'layered':
        // 通用分层显示
        const parts = value.split(' ');
        const generalLineHeight = fieldStyle.lineHeight || 1.2;
        const generalFontSize = fieldStyle.fontSize || 12;

        if (parts.length >= 2) {
          const firstPart = parts[0];
          const restParts = parts.slice(1).join(' ');

          return `
            <div style="text-align: center; line-height: ${generalLineHeight};">
              <div style="font-size: ${generalFontSize + 6}px; font-weight: bold; margin-bottom: 2px;">${firstPart}</div>
              <div style="font-size: ${generalFontSize - 2}px;">${restParts}</div>
            </div>
          `;
        } else {
          return `<span style="font-size: ${generalFontSize}px;">${value}</span>`;
        }

      default:
        return value;
    }
  };

  // 格式化评级信息显示（用于多字段显示配置）
  const formatGradeDisplay = (value, displayConfig) => {
    const gradeInfo = parseGradeInfo(value);
    const fontSize = displayConfig.fontSize || 12;
    const letterSpacing = displayConfig.letterSpacing || 2;

    switch (displayConfig.displayMode) {
      case 'grade-score-only':
        return gradeInfo.score ?
          `<span style="font-size: ${fontSize}px; font-weight: bold;">${gradeInfo.score}</span>` :
          '';

      case 'grade-level-only':
        return gradeInfo.level ?
          `<span style="font-size: ${fontSize}px;">${gradeInfo.level}</span>` :
          '';

      case 'grade-mark-only':
        if (gradeInfo.mark) {
          const chars = gradeInfo.mark.split('');
          return chars.map(char =>
            `<div style="line-height: 1; margin-bottom: ${letterSpacing}px; font-size: ${fontSize}px;">${char}</div>`
          ).join('');
        }
        return '';

      default:
        return value || '';
    }
  };

  // 获取评级显示样式
  const getGradeDisplayStyle = (displayConfig) => {
    const baseStyle = {
      position: 'absolute',
      fontSize: `${displayConfig.fontSize || 12}px`
    };

    // 根据位置设置样式
    switch (displayConfig.position) {
      case 'top':
        return {
          ...baseStyle,
          top: '2px',
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center'
        };

      case 'middle':
        return {
          ...baseStyle,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center'
        };

      case 'bottom':
        return {
          ...baseStyle,
          bottom: '2px',
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center'
        };

      case 'right':
        return {
          ...baseStyle,
          top: '2px',
          right: '2px',
          textAlign: 'center'
        };

      case 'left':
        return {
          ...baseStyle,
          top: '2px',
          left: '2px',
          textAlign: 'center'
        };

      default:
        return baseStyle;
    }
  };

  // 格式化二维码显示
  const formatQRCode = (value, fieldStyle) => {
    if (!value) return '';

    const size = fieldStyle?.qrSize || 60; // 默认二维码大小
    const showText = fieldStyle?.showText !== false; // 默认显示文本
    const textSize = fieldStyle?.textSize || 10; // 文本大小

    // 生成二维码HTML
    let qrHtml = `<div class="qr-code-container" style="text-align: center;">`;

    // 二维码图片占位符（实际应用中需要集成二维码生成库）
    qrHtml += `<div class="qr-code-placeholder" style="
      width: ${size}px;
      height: ${size}px;
      border: 2px solid #000;
      margin: 0 auto 4px auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      background: url('data:image/svg+xml;base64,${generateQRCodeSVG(value)}') no-repeat center;
      background-size: contain;
    ">`;

    // 如果无法生成二维码，显示占位符
    qrHtml += `<span style="font-size: 8px; color: #666;">QR</span>`;
    qrHtml += `</div>`;

    // 显示二维码内容文本
    if (showText) {
      qrHtml += `<div class="qr-code-text" style="
        font-size: ${textSize}px;
        line-height: 1.2;
        word-break: break-all;
        margin-top: 2px;
      ">${value}</div>`;
    }

    qrHtml += `</div>`;

    return qrHtml;
  };

  // 生成简单的二维码SVG（占位符实现）
  const generateQRCodeSVG = (text) => {
    // 这里是一个简化的二维码SVG生成，实际应用中应该使用专业的二维码库
    const svg = `
      <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100" height="100" fill="white"/>
        <rect x="10" y="10" width="20" height="20" fill="black"/>
        <rect x="70" y="10" width="20" height="20" fill="black"/>
        <rect x="10" y="70" width="20" height="20" fill="black"/>
        <rect x="40" y="40" width="20" height="20" fill="black"/>
        <!-- 更多二维码模式点... -->
        <text x="50" y="95" text-anchor="middle" font-size="8" fill="black">${text.substring(0, 10)}</text>
      </svg>
    `;
    return btoa(svg);
  };

  // 判断是否为二维码字段
  const isQRCodeField = (fieldName) => {
    return fieldName === 'qrCode' || fieldName === 'qrCodeContent';
  };

  // 获取二维码大小
  const getQRCodeSize = (zone, fieldName) => {
    const fieldStyle = zone?.fieldStyles?.[fieldName];
    return fieldStyle?.qrSize || 60;
  };

  // 获取二维码是否显示文本
  const getQRCodeShowText = (zone, fieldName) => {
    const fieldStyle = zone?.fieldStyles?.[fieldName];
    return fieldStyle?.showText !== false;
  };

  // 获取二维码文本大小
  const getQRCodeTextSize = (zone, fieldName) => {
    const fieldStyle = zone?.fieldStyles?.[fieldName];
    return fieldStyle?.textSize || 10;
  };

  // 提取评级分数的数字部分
  const extractGradeScoreNumber = (gradeScore) => {
    if (!gradeScore) return '';

    console.log('原始评级分数数据:', gradeScore, '类型:', typeof gradeScore);

    // 提取数字部分，支持整数和小数
    const numberMatch = gradeScore.toString().match(/\d+(\.\d+)?/);
    const result = numberMatch ? numberMatch[0] : '';

    console.log('提取的数字部分:', result);
    return result;
  };

  // 提取评级分数的文字部分
  const extractGradeScoreText = (gradeScore, item = null) => {
    console.log('=== 提取评级文字部分 ===');
    console.log('gradeScore:', gradeScore);
    console.log('item对象:', item);

    if (!gradeScore && !item) return '';

    let textPart = '';

    // 如果有gradeScore，先尝试从中提取文字
    if (gradeScore) {
      const gradeStr = gradeScore.toString().trim();
      console.log('gradeScore字符串:', gradeStr);

      // 方式1: 如果数字在末尾 (如 "Superb Gem Unc68")
      const endNumberMatch = gradeStr.match(/^(.+?)(\d+(\.\d+)?)$/);
      if (endNumberMatch && endNumberMatch[1].trim()) {
        textPart = endNumberMatch[1].trim();
        console.log('数字在末尾格式，提取文字:', textPart);
      }

      // 方式2: 如果数字在开头 (如 "68 Superb Gem Unc")
      if (!textPart) {
        const startNumberMatch = gradeStr.match(/^(\d+(\.\d+)?)\s*(.+)$/);
        if (startNumberMatch && startNumberMatch[3].trim()) {
          textPart = startNumberMatch[3].trim();
          console.log('数字在开头格式，提取文字:', textPart);
        }
      }

      // 方式3: 简单移除所有数字 (如 "MS68" -> "MS")
      if (!textPart) {
        const cleanText = gradeStr.replace(/\d+(\.\d+)?/g, '').trim();
        if (cleanText && cleanText.length > 0) {
          textPart = cleanText;
          console.log('简单移除数字，提取文字:', textPart);
        }
      }

      // 方式4: 如果还是为空，可能整个字段就是文字
      if (!textPart && !/\d/.test(gradeStr)) {
        textPart = gradeStr;
        console.log('整个字段为文字:', textPart);
      }
    }

    // 如果从gradeScore中没有提取到文字，尝试从其他字段获取
    if (!textPart && item) {
      console.log('从gradeScore中未提取到文字，尝试其他字段...');

      // 优先级：specialMark > rank > gradeLevel
      if (item.specialMark && item.specialMark.trim()) {
        textPart = item.specialMark.trim();
        console.log('从specialMark获取:', textPart);
      } else if (item.rank && item.rank.trim()) {
        textPart = item.rank.trim();
        console.log('从rank获取:', textPart);
      } else if (item.gradeLevel && item.gradeLevel.trim()) {
        textPart = item.gradeLevel.trim();
        console.log('从gradeLevel获取:', textPart);
      }
    }

    console.log('最终提取的文字部分:', textPart);
    return textPart || '';
  };



  // 格式化二维码显示（简化版本）
  const formatQRCodeDisplay = (field, zone) => {
    const value = field.value || '';
    if (!value) return '';

    const size = getQRCodeSize(zone, field.name);
    const showText = getQRCodeShowText(zone, field.name);
    const textSize = getQRCodeTextSize(zone, field.name);

    // 生成简化的二维码HTML（使用在线二维码API或占位符）
    let qrHtml = `<div class="qr-code-container" style="text-align: center;">`;

    // 使用在线二维码生成服务（示例）
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(value)}`;

    qrHtml += `<img src="${qrCodeUrl}" alt="QR Code" style="
      width: ${size}px;
      height: ${size}px;
      border: 1px solid #ddd;
      display: block;
      margin: 0 auto;
    " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />`;

    // 备用占位符
    qrHtml += `<div style="
      width: ${size}px;
      height: ${size}px;
      border: 2px solid #000;
      margin: 0 auto;
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      background: repeating-linear-gradient(45deg, #000, #000 2px, #fff 2px, #fff 4px);
    ">QR</div>`;

    // 显示二维码内容文本
    if (showText) {
      qrHtml += `<div class="qr-code-text" style="
        font-size: ${textSize}px;
        line-height: 1.2;
        word-break: break-all;
        margin-top: 2px;
        text-align: center;
      ">${value}</div>`;
    }

    qrHtml += `</div>`;

    return qrHtml;
  };

  // 替换模板中的字段变量为具体值
  const replaceTemplateFields = (template, coinData) => {
    if (!template || !coinData) {
      console.warn('模板或钱币数据为空');
      return template;
    }

    try {
      // 深拷贝模板以避免修改原始数据
      const processedTemplate = JSON.parse(JSON.stringify(template));

      // 创建字段值映射
      const fieldValues = {
        // 基础信息
        bankName: coinData.bankName || '',
        coinName: coinData.coinName || '',
        coinName1: coinData.coinName || '',
        yearInfo: coinData.yearInfo || '',
        serialNumber: coinData.serialNumber || '',
        version: coinData.version || '',
        additionalInfo: coinData.additionalInfo || '',

        // 评级信息
        gradeScore: coinData.gradeScore || '',
        gradeLevel: coinData.gradeLevel || '',
        specialMark: coinData.specialMark || '',
        authenticity: coinData.authenticity || '',

        // 客户信息
        customerName: coinData.customerName || '',
        diyCode: coinData.diyCode || '',

        // 物理属性
        weight: coinData.weight || '',
        size: coinData.size || '',
        coinType: coinData.coinType || '',

        // 二维码相关
        qrcode: coinData.diyCode || '', // 二维码内容使用送评条码
        qrCodeContent: coinData.diyCode || ''
      };

      // 处理面板数据
      if (processedTemplate.panels && Array.isArray(processedTemplate.panels)) {
        processedTemplate.panels.forEach(panel => {
          if (panel.printElements && Array.isArray(panel.printElements)) {
            panel.printElements.forEach(element => {
              if (element.options && element.options.field) {
                // 替换field中的变量
                const fieldName = element.options.field.replace(/[{}]/g, ''); // 移除大括号
                if (fieldValues.hasOwnProperty(fieldName)) {
                  element.options.field = fieldValues[fieldName];
                  // 同时更新title显示
                  if (!element.options.title || element.options.title.includes('{')) {
                    element.options.title = fieldValues[fieldName];
                  }
                }
              }

              // 处理title中的变量
              if (element.options && element.options.title && typeof element.options.title === 'string') {
                element.options.title = element.options.title.replace(/\{(\w+)\}/g, (match, fieldName) => {
                  return fieldValues[fieldName] || match;
                });
              }

              // 处理text中的变量
              if (element.options && element.options.text && typeof element.options.text === 'string') {
                element.options.text = element.options.text.replace(/\{(\w+)\}/g, (match, fieldName) => {
                  return fieldValues[fieldName] || match;
                });
              }
            });
          }
        });
      }

      console.log('模板字段替换完成:', {
        原始模板: template,
        处理后模板: processedTemplate,
        字段值映射: fieldValues
      });

      return processedTemplate;
    } catch (error) {
      console.error('替换模板字段时发生错误:', error);
      return template;
    }
  };
</script>

<style scoped>
  /* 抽屉头部样式 */
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .drawer-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .drawer-actions {
    display: flex;
    gap: 8px;
  }

  /* 打印内容区域 */
  .print-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
  }

  .print-info-bar {
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  .print-page {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
  }

  /* 自定义模板容器样式 */
  .custom-template-container {
    width: 100%;
  }

  .custom-label-item {
    margin-bottom: 8px;
    transition: all 0.2s;
  }

  .custom-label-item:hover {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }

  .template-zone {
    box-sizing: border-box;
  }

  .zone-field {
    font-size: inherit;
    line-height: 1.2;
  }

  .grade-info-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .grade-display-item {
    position: absolute;
  }

  .grade-top {
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }

  .grade-middle {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .grade-bottom {
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }

  .grade-right {
    top: 2px;
    right: 2px;
    text-align: center;
  }

  .grade-left {
    top: 2px;
    left: 2px;
    text-align: center;
  }

  /* 预定义模板样式（保持原有样式） */
  .predefined-template-container {
    width: 100%;
  }

  .label-item {
    width: 100%;
    height: 80px;
    border: 1px solid #dcdfe6;
    border-top: none;
    display: flex;
    align-items: center;
    page-break-inside: avoid;
    background: white;
    transition: all 0.2s;
  }

  .label-item:hover {
    background: #f8f9fa;
    border-color: #409eff;
  }

  .label-item:first-child {
    border-top: 1px solid #dcdfe6;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .label-item:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .label-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 8px 16px;
  }

  /* Logo区域样式 */
  .logo-section {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .cmg-logo {
    text-align: center;
  }

  .logo-symbol {
    font-size: 18px;
    font-weight: bold;
    color: #d4af37;
    line-height: 1;
    margin-bottom: 2px;
  }

  .logo-text {
    font-size: 8px;
    color: #606266;
    line-height: 1;
  }

  /* 钱币信息样式 */
  .coin-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: left;
    padding-left: 16px;
  }

  .bank-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 4px;
    color: #303133;
  }

  .coin-year {
    font-size: 12px;
    margin-bottom: 2px;
    color: #606266;
  }

  .coin-serial {
    font-size: 10px;
    color: #909399;
  }

  /* 评级和二维码区域样式 */
  .grade-qr-section {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .grade-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
  }

  .grade-score {
    font-size: 36px;
    font-weight: bold;
    color: #e6a23c;
    line-height: 1;
    margin-bottom: 2px;
  }

  .grade-level-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  .grade-level {
    font-size: 8px;
    color: #606266;
    text-align: center;
    margin-bottom: 2px;
    max-width: 80px;
    word-wrap: break-word;
  }

  .special-mark {
    font-size: 6px;
    color: #909399;
    writing-mode: vertical-rl;
    text-orientation: upright;
    line-height: 1;
    margin-left: 2px;
  }

  /* 二维码样式 */
  .qr-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
  }

  .qr-code {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qr-placeholder {
    width: 60px;
    height: 60px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
    background: white;
  }

  .qr-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 1px;
    width: 50px;
    height: 50px;
  }

  .qr-dot {
    background: white;
  }

  .qr-dot.active {
    background: black;
  }

  .qr-number {
    font-size: 8px;
    color: #909399;
    text-align: center;
    max-width: 80px;
    word-break: break-all;
  }



  /* 打印样式 */
  @media print {
    :deep(.el-drawer) {
      position: static !important;
      transform: none !important;
      width: 100% !important;
      height: auto !important;
      box-shadow: none !important;
    }

    :deep(.el-drawer__header) {
      display: none !important;
    }

    :deep(.el-drawer__body) {
      padding: 0 !important;
    }

    .print-content {
      background: white !important;
      height: auto !important;
    }

    .print-info-bar {
      display: none !important;
    }

    .print-page {
      box-shadow: none !important;
      border-radius: 0 !important;
      padding: 1cm !important;
      width: 21cm !important;
      margin: 0 auto !important;
    }

    /* 自定义模板打印样式 */
    .custom-label-item {
      margin-bottom: 2mm !important;
      border: 1px solid #999 !important;
      page-break-inside: avoid;
    }

    .template-zone {
      border: none !important;
    }

    /* 预定义模板打印样式 */
    .label-item {
      height: 2.5cm !important;
      border-color: #999 !important;
      background: white !important;
      page-break-inside: avoid;
    }

    .label-item:first-child {
      height: 3.7cm !important;
    }

    .label-item:hover {
      background: white !important;
      border-color: #999 !important;
    }

    .logo-symbol {
      font-size: 16px !important;
    }

    .logo-text {
      font-size: 6px !important;
    }
  }
</style>
