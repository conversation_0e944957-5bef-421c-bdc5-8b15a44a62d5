/**
 * 模板字段替换工具
 * 用于将模板中的字段变量（如{bankName}）替换为具体的数据值
 * 
 * <AUTHOR>
 * @since 2025-01-28
 */

/**
 * 创建字段值映射
 * @param {Object} coinData 钱币数据对象
 * @returns {Object} 字段值映射对象
 */
export function createFieldValueMapping(coinData) {
  if (!coinData) {
    console.warn('钱币数据为空，返回空映射');
    return {};
  }

  return {
    // 基础信息
    bankName: coinData.bankName || '',
    coinName: coinData.coinName || '',
    coinName1: coinData.coinName || '',
    coinName2: coinData.coinName2 || '',
    coinName3: coinData.coinName3 || '',
    yearInfo: coinData.yearInfo || '',
    serialNumber: coinData.serialNumber || '',
    version: coinData.version || '',
    additionalInfo: coinData.additionalInfo || '',
    
    // 评级信息
    gradeScore: coinData.gradeScore || '',
    gradeLevel: coinData.gradeLevel || '',
    specialMark: coinData.specialMark || '',
    authenticity: coinData.authenticity || '',
    rank: coinData.rank || '',
    
    // 客户信息
    customerName: coinData.customerName || '',
    diyCode: coinData.diyCode || '',
    
    // 物理属性
    weight: coinData.weight || '',
    size: coinData.size || '',
    coinType: coinData.coinType || '',
    
    // 二维码相关
    qrcode: coinData.diyCode || '', // 二维码内容使用送评条码
    qrCodeContent: coinData.diyCode || '',
    
    // 其他字段
    sendformNumber: coinData.sendformNumber || '',
    fee: coinData.fee || '',
    auditStatus: coinData.auditStatus || '',
    createTime: coinData.createTime || ''
  };
}

/**
 * 替换字符串中的字段变量
 * @param {string} text 包含变量的文本
 * @param {Object} fieldValues 字段值映射
 * @returns {string} 替换后的文本
 */
export function replaceFieldVariables(text, fieldValues) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  return text.replace(/\{(\w+)\}/g, (match, fieldName) => {
    if (fieldValues.hasOwnProperty(fieldName)) {
      return fieldValues[fieldName] || '';
    }
    console.warn(`未找到字段 ${fieldName} 的值，保持原样`);
    return match;
  });
}

/**
 * 处理单个打印元素的字段替换
 * @param {Object} element 打印元素对象
 * @param {Object} fieldValues 字段值映射
 */
export function processElementFields(element, fieldValues) {
  if (!element || !element.options) {
    return;
  }

  const options = element.options;

  // 处理field字段
  if (options.field && typeof options.field === 'string') {
    const fieldName = options.field.replace(/[{}]/g, ''); // 移除大括号
    if (fieldValues.hasOwnProperty(fieldName)) {
      options.field = fieldValues[fieldName];
      // 同时更新title显示（如果title为空或包含变量）
      if (!options.title || options.title.includes('{')) {
        options.title = fieldValues[fieldName];
      }
    }
  }

  // 处理title字段
  if (options.title && typeof options.title === 'string') {
    options.title = replaceFieldVariables(options.title, fieldValues);
  }

  // 处理text字段
  if (options.text && typeof options.text === 'string') {
    options.text = replaceFieldVariables(options.text, fieldValues);
  }

  // 处理其他可能包含变量的字段
  const fieldsToProcess = ['content', 'value', 'placeholder', 'defaultValue'];
  fieldsToProcess.forEach(fieldName => {
    if (options[fieldName] && typeof options[fieldName] === 'string') {
      options[fieldName] = replaceFieldVariables(options[fieldName], fieldValues);
    }
  });
}

/**
 * 替换模板中的字段变量为具体值
 * @param {Object} template 模板对象
 * @param {Object} coinData 钱币数据对象
 * @returns {Object} 处理后的模板对象
 */
export function replaceTemplateFields(template, coinData) {
  if (!template || !coinData) {
    console.warn('模板或钱币数据为空');
    return template;
  }

  try {
    // 深拷贝模板以避免修改原始数据
    const processedTemplate = JSON.parse(JSON.stringify(template));
    
    // 创建字段值映射
    const fieldValues = createFieldValueMapping(coinData);

    console.log('开始处理模板字段替换:', {
      模板类型: typeof processedTemplate,
      钱币数据: coinData,
      字段映射: fieldValues
    });

    // 处理面板数据
    if (processedTemplate.panels && Array.isArray(processedTemplate.panels)) {
      processedTemplate.panels.forEach((panel, panelIndex) => {
        console.log(`处理面板 ${panelIndex}:`, panel);
        
        if (panel.printElements && Array.isArray(panel.printElements)) {
          panel.printElements.forEach((element, elementIndex) => {
            console.log(`处理元素 ${elementIndex}:`, element);
            processElementFields(element, fieldValues);
          });
        }
      });
    }

    console.log('模板字段替换完成:', {
      原始模板: template,
      处理后模板: processedTemplate
    });

    return processedTemplate;
  } catch (error) {
    console.error('替换模板字段时发生错误:', error);
    return template;
  }
}

/**
 * 处理hiprint模板数据，为多个钱币数据创建对应的模板
 * @param {Object} templateData 原始模板数据
 * @param {Array} coinDataList 钱币数据列表
 * @returns {Array} 处理后的模板列表
 */
export function processHiprintTemplate(templateData, coinDataList) {
  if (!templateData || !coinDataList || !Array.isArray(coinDataList)) {
    console.warn('模板数据或钱币数据列表为空');
    return [];
  }

  try {
    console.log('开始处理hiprint模板:', {
      模板数据: templateData,
      钱币数量: coinDataList.length
    });

    // 为每个钱币数据创建一个模板副本
    const processedTemplates = coinDataList.map((coinData, index) => {
      console.log(`处理第 ${index + 1} 个钱币数据:`, coinData);
      return replaceTemplateFields(templateData, coinData);
    });

    console.log('所有模板处理完成:', processedTemplates);
    return processedTemplates;
  } catch (error) {
    console.error('处理hiprint模板时发生错误:', error);
    return [];
  }
}

/**
 * 批量替换模板字段（用于单个模板对应多个数据项的情况）
 * @param {Object} template 模板对象
 * @param {Array} dataList 数据列表
 * @returns {Array} 处理后的模板列表
 */
export function batchReplaceTemplateFields(template, dataList) {
  if (!template || !dataList || !Array.isArray(dataList)) {
    console.warn('模板或数据列表为空');
    return [];
  }

  return dataList.map((data, index) => {
    console.log(`批量处理第 ${index + 1} 项数据:`, data);
    return replaceTemplateFields(template, data);
  });
}

/**
 * 验证模板数据结构
 * @param {Object} template 模板对象
 * @returns {boolean} 是否为有效的模板结构
 */
export function validateTemplateStructure(template) {
  if (!template || typeof template !== 'object') {
    return false;
  }

  // 检查是否有panels数组
  if (!template.panels || !Array.isArray(template.panels)) {
    return false;
  }

  // 检查每个面板是否有printElements
  return template.panels.every(panel => 
    panel && typeof panel === 'object' && 
    Array.isArray(panel.printElements)
  );
}

/**
 * 获取模板中所有的字段变量
 * @param {Object} template 模板对象
 * @returns {Set} 字段变量集合
 */
export function extractTemplateFields(template) {
  const fields = new Set();
  
  if (!validateTemplateStructure(template)) {
    return fields;
  }

  template.panels.forEach(panel => {
    panel.printElements.forEach(element => {
      if (element.options) {
        const options = element.options;
        
        // 检查各个可能包含变量的字段
        const fieldsToCheck = ['field', 'title', 'text', 'content', 'value'];
        fieldsToCheck.forEach(fieldName => {
          if (options[fieldName] && typeof options[fieldName] === 'string') {
            const matches = options[fieldName].match(/\{(\w+)\}/g);
            if (matches) {
              matches.forEach(match => {
                const fieldName = match.replace(/[{}]/g, '');
                fields.add(fieldName);
              });
            }
          }
        });
      }
    });
  });

  return fields;
}

export default {
  createFieldValueMapping,
  replaceFieldVariables,
  processElementFields,
  replaceTemplateFields,
  processHiprintTemplate,
  batchReplaceTemplateFields,
  validateTemplateStructure,
  extractTemplateFields
};
